{"compilerOptions": {"strict": true, "module": "commonjs", "moduleResolution": "node", "target": "es2019", "lib": ["es2019", "es2020", "es2022.error"], "removeComments": true, "useUnknownInCatchVariables": false, "forceConsistentCasingInFileNames": true, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": true, "strictNullChecks": true, "preserveConstEnums": true, "esModuleInterop": true, "resolveJsonModule": true, "incremental": true, "declaration": true, "sourceMap": true, "skipLibCheck": true, "outDir": "./dist/"}, "include": ["credentials/**/*", "nodes/**/*", "nodes/**/*.json", "package.json"]}