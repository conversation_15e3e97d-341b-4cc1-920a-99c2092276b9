{"name": "n8n-nodes-kylas", "version": "1.0.1", "description": "n8n community node to work with Kylas CRM API - lead management, sales automation, and customer relationship management", "license": "MIT", "homepage": "https://github.com/kylastech/n8n-nodes-kylas", "keywords": ["n8n-community-node-package", "kylas", "crm", "sales", "lead-management", "automation"], "author": {"name": "alpeshjikadra", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/kylastech/n8n-nodes-kylas.git"}, "scripts": {"build": "n8n-node build", "build:watch": "tsc --watch", "dev": "n8n-node dev", "lint": "n8n-node lint", "lint:fix": "n8n-node lint --fix", "release": "n8n-node release", "prepublishOnly": "n8n-node prerelease", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "node test/integration.test.js"}, "files": ["dist", "README.md", "CHANGELOG.md"], "n8n": {"n8nNodesApiVersion": 1, "credentials": ["dist/credentials/KylasApi.credentials.js"], "nodes": ["dist/nodes/<PERSON>yla<PERSON>/<PERSON>yla<PERSON>.node.js"]}, "devDependencies": {"@n8n/node-cli": "*", "eslint": "9.32.0", "jest": "^29.7.0", "prettier": "3.6.2", "release-it": "^19.0.4", "typescript": "5.9.2"}, "peerDependencies": {"n8n-workflow": "*"}}